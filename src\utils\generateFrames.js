/**
 * 生成动画帧的工具函数
 * 由于我们没有实际的视频帧，这里生成一些示例图片帧
 */

// 生成SVG动画帧
export function generateAnimationFrames(totalFrames = 60) {
  const frames = []
  
  for (let i = 0; i < totalFrames; i++) {
    const progress = i / (totalFrames - 1)
    const frame = generateSVGFrame(progress, i)
    frames.push(frame)
  }
  
  return frames
}

// 生成单个SVG帧
function generateSVGFrame(progress, frameIndex) {
  const size = 800
  const centerX = size / 2
  const centerY = size / 2
  
  // 创建动画效果：旋转的几何图形
  const rotation = progress * 360 * 2 // 两圈旋转
  const scale = 0.5 + progress * 0.5 // 从50%缩放到100%
  const opacity = Math.sin(progress * Math.PI * 4) * 0.3 + 0.7 // 脉动效果
  
  // 颜色渐变
  const hue = progress * 360
  const color1 = `hsl(${hue}, 70%, 60%)`
  const color2 = `hsl(${(hue + 60) % 360}, 70%, 40%)`
  
  // 创建多个几何形状
  const shapes = []
  
  // 中心圆形
  shapes.push(`
    <circle 
      cx="${centerX}" 
      cy="${centerY}" 
      r="${50 * scale}" 
      fill="${color1}" 
      opacity="${opacity}"
      transform="rotate(${rotation} ${centerX} ${centerY})"
    />
  `)
  
  // 围绕的小圆
  for (let j = 0; j < 6; j++) {
    const angle = (j / 6) * Math.PI * 2 + (progress * Math.PI * 2)
    const radius = 120 * scale
    const x = centerX + Math.cos(angle) * radius
    const y = centerY + Math.sin(angle) * radius
    
    shapes.push(`
      <circle 
        cx="${x}" 
        cy="${y}" 
        r="${20 * scale}" 
        fill="${color2}" 
        opacity="${opacity * 0.8}"
      />
    `)
  }
  
  // 背景矩形
  const bgOpacity = 0.1 + progress * 0.1
  shapes.unshift(`
    <rect 
      width="${size}" 
      height="${size}" 
      fill="url(#gradient${frameIndex})"
      opacity="${bgOpacity}"
    />
  `)
  
  // 渐变定义
  const gradient = `
    <defs>
      <linearGradient id="gradient${frameIndex}" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${color1};stop-opacity:0.3" />
        <stop offset="100%" style="stop-color:${color2};stop-opacity:0.1" />
      </linearGradient>
    </defs>
  `
  
  const svg = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      ${gradient}
      ${shapes.join('')}
      <text x="${centerX}" y="${centerY + 200}" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="${color1}" opacity="0.8">
        Frame ${frameIndex + 1}/${60}
      </text>
    </svg>
  `
  
  // 转换为data URL
  const dataUrl = `data:image/svg+xml;base64,${btoa(svg)}`
  
  return {
    index: frameIndex,
    progress: progress,
    dataUrl: dataUrl,
    svg: svg
  }
}

// 预加载图片帧
export function preloadFrames(frames) {
  return Promise.all(
    frames.map(frame => {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve(frame)
        img.onerror = reject
        img.src = frame.dataUrl
      })
    })
  )
}

// 计算滚动进度
export function calculateScrollProgress(element, container = window) {
  const rect = element.getBoundingClientRect()
  const containerHeight = container.innerHeight || container.clientHeight
  
  // 计算元素在视口中的位置
  const elementTop = rect.top
  const elementHeight = rect.height
  
  // 当元素完全进入视口时开始动画，完全离开时结束
  const startPoint = containerHeight
  const endPoint = -elementHeight
  const totalDistance = startPoint - endPoint
  
  // 计算当前进度 (0-1)
  const currentPosition = startPoint - elementTop
  const progress = Math.max(0, Math.min(1, currentPosition / totalDistance))
  
  return progress
}
