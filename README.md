# 滚动驱动逐帧动画演示

这是一个使用 Vue 3 + Vite 构建的滚动驱动逐帧动画演示项目，灵感来源于 [Adaline.ai](https://www.adaline.ai/) 网站的动画效果。

## 🎯 项目特性

- **滚动驱动动画**：通过页面滚动位置控制动画播放进度
- **双版本演示**：
  - 基础版本：使用预生成的 SVG 图片帧
  - 增强版本：使用 Canvas 实时绘制 + 粒子效果
- **高性能**：60fps 流畅动画，节流优化滚动监听
- **响应式设计**：完美适配桌面端和移动端
- **现代技术栈**：Vue 3 Composition API + Vite

## 🚀 快速开始

### 安装依赖
```bash
npm install
# 或
pnpm install
```

### 启动开发服务器
```bash
npm run dev
# 或
pnpm dev
```

### 构建生产版本
```bash
npm run build
# 或
pnpm build
```

## 📁 项目结构

```
src/
├── components/
│   ├── ScrollFrameAnimation.vue    # 基础版滚动动画组件
│   └── EnhancedScrollAnimation.vue # 增强版滚动动画组件
├── utils/
│   └── generateFrames.js          # 动画帧生成工具
├── App.vue                        # 主应用组件
├── main.js                        # 应用入口
└── style.css                      # 全局样式
```

## 🎨 技术实现

### 基础版本特性
- 预生成 60 帧 SVG 动画图片
- 基于滚动进度的帧切换
- 图片预加载优化
- 进度指示器

### 增强版本特性
- Canvas 实时绘制几何图形
- 动态粒子背景效果
- 滚动驱动的文本变化
- 圆形进度指示器
- 多层视觉效果

### 核心算法
```javascript
// 滚动进度计算
function calculateScrollProgress(element) {
  const rect = element.getBoundingClientRect()
  const windowHeight = window.innerHeight
  const elementHeight = rect.height

  const startPoint = windowHeight
  const endPoint = -elementHeight
  const totalDistance = startPoint - endPoint
  const currentPosition = startPoint - rect.top

  return Math.max(0, Math.min(1, currentPosition / totalDistance))
}
```

## 🎯 使用场景

这种滚动驱动动画技术适用于：
- 产品展示页面
- 品牌故事讲述
- 数据可视化展示
- 交互式教程
- 创意作品集

## 🛠️ 自定义开发

### 修改动画帧数
在 `generateFrames.js` 中修改 `totalFrames` 参数：
```javascript
export function generateAnimationFrames(totalFrames = 60) {
  // 你的自定义帧数
}
```

### 自定义动画内容
在 `EnhancedScrollAnimation.vue` 的 `drawFrame` 函数中修改绘制逻辑：
```javascript
const drawFrame = (ctx, progress) => {
  // 你的自定义绘制代码
}
```

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
