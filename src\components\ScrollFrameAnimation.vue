<template>
  <div class="scroll-animation-container">
    <!-- 全屏动画区域 -->
    <div class="animation-section" ref="animationSection">
      <div class="animation-content">
        <!-- 主要动画帧 -->
        <div class="frame-container" ref="frameContainer">
          <img
            src="/src/assets/img/4k.jpg"
            alt="4K Animation Frame"
            class="animation-frame"
            :style="frameStyle"
          />
          <div class="loading-overlay" v-if="isLoading">
            <div class="loading-spinner"></div>
            <p>初始化中... {{ loadingProgress }}%</p>
          </div>
        </div>

        <!-- 渐进式内容展示 -->
        <div class="progressive-content">
          <!-- 标题 - 在滚动10%时出现 -->
          <div
            class="content-item title-section"
            :style="getTitleStyle()"
            v-if="scrollProgress > 0.1"
          >
            <h1>滚动驱动动画</h1>
            <p>体验流畅的逐帧动画效果</p>
          </div>

          <!-- 特性介绍 - 在滚动30%时出现 -->
          <div
            class="content-item features-section"
            :style="getFeaturesStyle()"
            v-if="scrollProgress > 0.3"
          >
            <h2>核心特性</h2>
            <div class="feature-grid">
              <div class="feature-item" :style="getFeatureItemStyle(0)">
                <div class="feature-icon">🎨</div>
                <h3>SVG 动画帧</h3>
                <p>60帧流畅动画</p>
              </div>
              <div class="feature-item" :style="getFeatureItemStyle(1)">
                <div class="feature-icon">⚡</div>
                <h3>高性能</h3>
                <p>优化的滚动监听</p>
              </div>
              <div class="feature-item" :style="getFeatureItemStyle(2)">
                <div class="feature-icon">📱</div>
                <h3>响应式</h3>
                <p>完美适配移动端</p>
              </div>
            </div>
          </div>

          <!-- 技术说明 - 在滚动60%时出现 -->
          <div
            class="content-item tech-section"
            :style="getTechStyle()"
            v-if="scrollProgress > 0.6"
          >
            <h2>技术实现</h2>
            <div class="tech-details">
              <div class="tech-item">
                <strong>滚动监听</strong>
                <span>实时计算滚动进度</span>
              </div>
              <div class="tech-item">
                <strong>帧切换</strong>
                <span>基于进度的精确控制</span>
              </div>
              <div class="tech-item">
                <strong>性能优化</strong>
                <span>节流函数 + 预加载</span>
              </div>
            </div>
          </div>

          <!-- 完成提示 - 在滚动90%时出现 -->
          <div
            class="content-item completion-section"
            :style="getCompletionStyle()"
            v-if="scrollProgress > 0.9"
          >
            <h2>🎉 动画完成</h2>
            <p>您已体验完整的滚动驱动动画效果</p>
          </div>

          <!-- 调试信息 -->
          <div class="debug-info" v-if="scrollProgress > 0">
            <p>滚动进度: {{ Math.round(scrollProgress * 100) }}%</p>
            <p>当前帧: {{ currentFrameIndex + 1 }}/{{ totalFrames }}</p>
          </div>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-indicator" :style="getProgressStyle()">
          <div class="progress-circle">
            <svg viewBox="0 0 100 100" class="progress-svg">
              <circle
                cx="50" cy="50" r="45"
                fill="none"
                stroke="rgba(255,255,255,0.2)"
                stroke-width="2"
              />
              <circle
                cx="50" cy="50" r="45"
                fill="none"
                stroke="url(#progressGradient)"
                stroke-width="3"
                stroke-linecap="round"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="dashOffset"
                transform="rotate(-90 50 50)"
              />
              <defs>
                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#667eea"/>
                  <stop offset="100%" style="stop-color:#764ba2"/>
                </linearGradient>
              </defs>
            </svg>
            <div class="progress-percentage">{{ Math.round(scrollProgress * 100) }}%</div>
          </div>
          <div class="progress-text">
            帧: {{ currentFrameIndex + 1 }}/{{ totalFrames }}
          </div>
        </div>
      </div>
    </div>

    <!-- 滚动提示 -->
    <div class="scroll-hint" v-if="scrollProgress < 0.05">
      <div class="scroll-mouse">
        <div class="scroll-wheel"></div>
      </div>
      <p>向下滚动探索</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { calculateScrollProgress } from '../utils/generateFrames.js'

// 响应式数据
const animationSection = ref(null)
const frameContainer = ref(null)
const frames = ref([])
const currentFrameIndex = ref(0)
const scrollProgress = ref(0)
const isLoading = ref(true)
const loadingProgress = ref(0)
const totalFrames = 60

// 计算属性
const currentFrame = computed(() => {
  return frames.value[currentFrameIndex.value] || null
})

const frameStyle = computed(() => {
  const progress = scrollProgress.value
  return {
    transform: `scale(${0.8 + progress * 0.4}) rotate(${progress * 10}deg)`,
    opacity: 0.6 + progress * 0.4,
    filter: `
      brightness(${0.7 + progress * 0.5})
      contrast(${0.8 + progress * 0.4})
      saturate(${0.5 + progress * 0.8})
      hue-rotate(${progress * 60}deg)
    `
  }
})

// 圆形进度条相关
const circumference = 2 * Math.PI * 45
const dashOffset = computed(() => circumference - (scrollProgress.value * circumference))

// 渐进式内容展示的样式计算
const getTitleStyle = () => {
  const progress = Math.max(0, Math.min(1, (scrollProgress.value - 0.1) / 0.2))
  return {
    opacity: progress,
    transform: `translateY(${(1 - progress) * 50}px)`,
    transition: 'all 0.6s ease-out'
  }
}

const getFeaturesStyle = () => {
  const progress = Math.max(0, Math.min(1, (scrollProgress.value - 0.3) / 0.2))
  return {
    opacity: progress,
    transform: `translateY(${(1 - progress) * 50}px)`,
    transition: 'all 0.6s ease-out'
  }
}

const getFeatureItemStyle = (index) => {
  const baseProgress = Math.max(0, Math.min(1, (scrollProgress.value - 0.35) / 0.15))
  const itemProgress = Math.max(0, Math.min(1, baseProgress - (index * 0.1)))
  return {
    opacity: itemProgress,
    transform: `translateY(${(1 - itemProgress) * 30}px) scale(${0.9 + itemProgress * 0.1})`,
    transition: `all 0.6s ease-out`,
    transitionDelay: `${index * 0.1}s`
  }
}

const getTechStyle = () => {
  const progress = Math.max(0, Math.min(1, (scrollProgress.value - 0.6) / 0.2))
  return {
    opacity: progress,
    transform: `translateY(${(1 - progress) * 50}px)`,
    transition: 'all 0.6s ease-out'
  }
}

const getCompletionStyle = () => {
  const progress = Math.max(0, Math.min(1, (scrollProgress.value - 0.9) / 0.1))
  return {
    opacity: progress,
    transform: `translateY(${(1 - progress) * 50}px) scale(${0.9 + progress * 0.1})`,
    transition: 'all 0.8s ease-out'
  }
}

const getProgressStyle = () => {
  return {
    opacity: scrollProgress.value > 0.05 ? 1 : 0,
    transition: 'opacity 0.3s ease'
  }
}

// 滚动事件处理
const handleScroll = () => {
  if (!animationSection.value || isLoading.value) return
  
  const progress = calculateScrollProgress(animationSection.value)
  scrollProgress.value = progress
  
  // 根据滚动进度计算当前帧
  const frameIndex = Math.floor(progress * (totalFrames - 1))
  currentFrameIndex.value = Math.max(0, Math.min(totalFrames - 1, frameIndex))
}

// 节流函数
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

const throttledHandleScroll = throttle(handleScroll, 16) // ~60fps

// 初始化
const initialize = async () => {
  try {
    isLoading.value = true
    loadingProgress.value = 0

    // 模拟加载过程
    for (let i = 0; i <= 100; i += 10) {
      loadingProgress.value = i
      await new Promise(resolve => setTimeout(resolve, 50))
    }

    isLoading.value = false

    // 初始化滚动监听
    handleScroll()
  } catch (error) {
    console.error('Failed to initialize:', error)
    isLoading.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  initialize()
  window.addEventListener('scroll', throttledHandleScroll, { passive: true })
  window.addEventListener('resize', throttledHandleScroll, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledHandleScroll)
  window.removeEventListener('resize', throttledHandleScroll)
})
</script>

<style scoped>
.scroll-animation-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
}

.animation-section {
  height: 400vh; /* 4倍视口高度，提供足够的滚动空间 */
  position: relative;
}

.animation-content {
  position: sticky;
  top: 0;
  height: 100vh;
  width: 100vw;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr;
  gap: 2rem;
  padding: 2rem;
  align-items: center;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

.frame-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, rgba(0,0,0,0.2) 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  justify-self: end;
}

.progressive-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  max-width: 500px;
  justify-self: start;
  color: white;
}

.content-item {
  opacity: 0;
  transform: translateY(50px);
}

.title-section h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.title-section p {
  font-size: 1.3rem;
  opacity: 0.8;
  margin: 0;
  line-height: 1.4;
}

.features-section h2,
.tech-section h2,
.completion-section h2 {
  font-size: 2.2rem;
  margin: 0 0 1.5rem 0;
  font-weight: 600;
  color: #667eea;
}

.feature-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.feature-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 12px;
  flex-shrink: 0;
}

.feature-item h3 {
  font-size: 1.2rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.feature-item p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

.tech-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.tech-item strong {
  color: #667eea;
  font-weight: 600;
}

.tech-item span {
  opacity: 0.8;
  font-size: 0.9rem;
}

.completion-section {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.completion-section h2 {
  color: #f093fb;
  margin-bottom: 1rem;
}

.completion-section p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.debug-info {
  position: fixed;
  top: 50%;
  left: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-family: monospace;
  font-size: 0.9rem;
  z-index: 1000;
}

.debug-info p {
  margin: 0.5rem 0;
}

.animation-frame {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255,255,255,0.3);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-indicator {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  z-index: 100;
}

.progress-circle {
  width: 80px;
  height: 80px;
  position: relative;
}

.progress-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.progress-text {
  color: white;
  font-size: 0.8rem;
  opacity: 0.8;
  font-family: monospace;
  text-align: center;
}

.scroll-hint {
  position: fixed;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  z-index: 100;
  animation: bounce 2s infinite;
}

.scroll-mouse {
  width: 30px;
  height: 50px;
  border: 2px solid rgba(255,255,255,0.6);
  border-radius: 15px;
  margin: 0 auto 1rem;
  position: relative;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background: rgba(255,255,255,0.6);
  border-radius: 2px;
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  animation: scroll-wheel 2s infinite;
}

@keyframes scroll-wheel {
  0% {
    top: 8px;
    opacity: 1;
  }
  100% {
    top: 24px;
    opacity: 0;
  }
}

.scroll-hint p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .animation-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .frame-container {
    justify-self: center;
    max-width: 400px;
  }

  .progressive-content {
    justify-self: center;
    max-width: 100%;
    text-align: center;
  }

  .title-section h1 {
    font-size: 2.5rem;
  }

  .features-section h2,
  .tech-section h2,
  .completion-section h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .animation-content {
    padding: 1rem;
    gap: 1rem;
  }

  .frame-container {
    max-width: 300px;
  }

  .title-section h1 {
    font-size: 2rem;
  }

  .title-section p {
    font-size: 1.1rem;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .tech-item {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .progress-indicator {
    bottom: 1rem;
    right: 1rem;
  }

  .progress-circle {
    width: 60px;
    height: 60px;
  }

  .progress-percentage {
    font-size: 0.8rem;
  }

  .progress-text {
    font-size: 0.7rem;
  }
}
</style>
