<template>
  <div class="scroll-animation-container">
    <!-- 动画区域 -->
    <div class="animation-section" ref="animationSection">
      <div class="animation-content">
        <div class="frame-container" ref="frameContainer">
          <img 
            v-if="currentFrame"
            :src="currentFrame.dataUrl" 
            :alt="`Animation frame ${currentFrame.index + 1}`"
            class="animation-frame"
            :style="frameStyle"
          />
          <div class="loading-overlay" v-if="isLoading">
            <div class="loading-spinner"></div>
            <p>加载动画帧中... {{ loadingProgress }}%</p>
          </div>
        </div>
        
        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: `${scrollProgress * 100}%` }"
            ></div>
          </div>
          <div class="progress-text">
            滚动进度: {{ Math.round(scrollProgress * 100) }}% 
            | 帧: {{ currentFrameIndex + 1 }}/{{ totalFrames }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 滚动提示 -->
    <div class="scroll-hint" v-if="scrollProgress === 0">
      <div class="scroll-arrow">↓</div>
      <p>向下滚动开始动画</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { generateAnimationFrames, preloadFrames, calculateScrollProgress } from '../utils/generateFrames.js'

// 响应式数据
const animationSection = ref(null)
const frameContainer = ref(null)
const frames = ref([])
const currentFrameIndex = ref(0)
const scrollProgress = ref(0)
const isLoading = ref(true)
const loadingProgress = ref(0)
const totalFrames = 60

// 计算属性
const currentFrame = computed(() => {
  return frames.value[currentFrameIndex.value] || null
})

const frameStyle = computed(() => {
  return {
    transform: `scale(${0.8 + scrollProgress.value * 0.2})`,
    opacity: 0.7 + scrollProgress.value * 0.3
  }
})

// 滚动事件处理
const handleScroll = () => {
  if (!animationSection.value || isLoading.value) return
  
  const progress = calculateScrollProgress(animationSection.value)
  scrollProgress.value = progress
  
  // 根据滚动进度计算当前帧
  const frameIndex = Math.floor(progress * (totalFrames - 1))
  currentFrameIndex.value = Math.max(0, Math.min(totalFrames - 1, frameIndex))
}

// 节流函数
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

const throttledHandleScroll = throttle(handleScroll, 16) // ~60fps

// 初始化动画帧
const initializeFrames = async () => {
  try {
    isLoading.value = true
    loadingProgress.value = 0
    
    // 生成帧数据
    const generatedFrames = generateAnimationFrames(totalFrames)
    
    // 预加载帧（分批加载以显示进度）
    const batchSize = 10
    const loadedFrames = []
    
    for (let i = 0; i < generatedFrames.length; i += batchSize) {
      const batch = generatedFrames.slice(i, i + batchSize)
      const loadedBatch = await preloadFrames(batch)
      loadedFrames.push(...loadedBatch)
      
      loadingProgress.value = Math.round((loadedFrames.length / generatedFrames.length) * 100)
    }
    
    frames.value = loadedFrames
    isLoading.value = false
    
    // 初始化滚动监听
    handleScroll()
  } catch (error) {
    console.error('Failed to initialize animation frames:', error)
    isLoading.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  initializeFrames()
  window.addEventListener('scroll', throttledHandleScroll, { passive: true })
  window.addEventListener('resize', throttledHandleScroll, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledHandleScroll)
  window.removeEventListener('resize', throttledHandleScroll)
})

// 监听滚动进度变化
watch(scrollProgress, (newProgress) => {
  // 可以在这里添加额外的动画效果
  if (frameContainer.value) {
    frameContainer.value.style.setProperty('--scroll-progress', newProgress)
  }
})
</script>

<style scoped>
.scroll-animation-container {
  position: relative;
}

.animation-section {
  height: 400vh; /* 4倍视口高度，提供足够的滚动空间 */
  position: relative;
}

.animation-content {
  position: sticky;
  top: 50%;
  transform: translateY(-50%);
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.frame-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(0,0,0,0.1) 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.animation-frame {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255,255,255,0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-indicator {
  margin-top: 2rem;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255,255,255,0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.1s ease-out;
  border-radius: 2px;
}

.progress-text {
  color: white;
  font-size: 0.9rem;
  opacity: 0.8;
  font-family: monospace;
}

.scroll-hint {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  z-index: 100;
  animation: bounce 2s infinite;
}

.scroll-arrow {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.scroll-hint p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .animation-content {
    padding: 1rem;
  }
  
  .frame-container {
    max-width: 90vw;
  }
  
  .progress-text {
    font-size: 0.8rem;
  }
}
</style>
