<template>
  <div class="enhanced-scroll-container">
    <!-- 背景粒子效果 -->
    <div class="particles-background" ref="particlesContainer">
      <div 
        v-for="particle in particles" 
        :key="particle.id"
        class="particle"
        :style="particle.style"
      ></div>
    </div>

    <!-- 主要动画区域 -->
    <div class="main-animation-area" ref="animationArea">
      <div class="sticky-content">
        <!-- 动画画布 -->
        <div class="animation-canvas" ref="canvas">
          <canvas 
            ref="canvasElement"
            :width="canvasSize.width"
            :height="canvasSize.height"
            class="frame-canvas"
          ></canvas>
          
          <!-- 覆盖层效果 -->
          <div class="canvas-overlay" :style="overlayStyle"></div>
        </div>

        <!-- 动态文本 -->
        <div class="dynamic-text" :style="textStyle">
          <h2>{{ currentText.title }}</h2>
          <p>{{ currentText.description }}</p>
        </div>

        <!-- 进度指示器 -->
        <div class="enhanced-progress">
          <div class="progress-circle" :style="circleStyle">
            <svg viewBox="0 0 100 100" class="progress-svg">
              <circle 
                cx="50" cy="50" r="45" 
                fill="none" 
                stroke="rgba(255,255,255,0.2)" 
                stroke-width="2"
              />
              <circle 
                cx="50" cy="50" r="45" 
                fill="none" 
                stroke="url(#progressGradient)" 
                stroke-width="3"
                stroke-linecap="round"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="dashOffset"
                transform="rotate(-90 50 50)"
              />
              <defs>
                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#667eea"/>
                  <stop offset="100%" style="stop-color:#764ba2"/>
                </linearGradient>
              </defs>
            </svg>
            <div class="progress-percentage">{{ Math.round(scrollProgress * 100) }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator" v-if="scrollProgress < 0.1">
      <div class="scroll-mouse">
        <div class="scroll-wheel"></div>
      </div>
      <p>滚动探索</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'

// 响应式数据
const animationArea = ref(null)
const canvasElement = ref(null)
const particlesContainer = ref(null)
const scrollProgress = ref(0)
const canvasSize = ref({ width: 800, height: 800 })
const particles = ref([])

// 动画文本内容
const textContent = [
  { title: "开始探索", description: "滚动开始您的旅程" },
  { title: "数据可视化", description: "观察数据的美丽变化" },
  { title: "交互体验", description: "感受流畅的动画效果" },
  { title: "创新设计", description: "体验现代网页技术" },
  { title: "完美结合", description: "技术与艺术的融合" }
]

// 计算属性
const currentText = computed(() => {
  const index = Math.floor(scrollProgress.value * (textContent.length - 1))
  return textContent[Math.min(index, textContent.length - 1)]
})

const overlayStyle = computed(() => ({
  background: `radial-gradient(circle, 
    rgba(102, 126, 234, ${0.1 + scrollProgress.value * 0.2}) 0%, 
    rgba(118, 75, 162, ${0.05 + scrollProgress.value * 0.1}) 100%)`
}))

const textStyle = computed(() => ({
  transform: `translateY(${(1 - scrollProgress.value) * 50}px)`,
  opacity: 0.6 + scrollProgress.value * 0.4
}))

const circleStyle = computed(() => ({
  transform: `scale(${0.8 + scrollProgress.value * 0.2}) rotate(${scrollProgress.value * 360}deg)`
}))

const circumference = 2 * Math.PI * 45
const dashOffset = computed(() => circumference - (scrollProgress.value * circumference))

// 粒子系统
const createParticles = () => {
  particles.value = Array.from({ length: 50 }, (_, i) => ({
    id: i,
    style: {
      left: `${Math.random() * 100}%`,
      top: `${Math.random() * 100}%`,
      animationDelay: `${Math.random() * 10}s`,
      animationDuration: `${10 + Math.random() * 20}s`
    }
  }))
}

// Canvas 绘制函数
const drawFrame = (ctx, progress) => {
  const { width, height } = canvasSize.value
  const centerX = width / 2
  const centerY = height / 2
  
  // 清除画布
  ctx.clearRect(0, 0, width, height)
  
  // 设置全局透明度
  ctx.globalAlpha = 0.8 + progress * 0.2
  
  // 绘制背景渐变
  const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(width, height) / 2)
  gradient.addColorStop(0, `hsla(${progress * 360}, 70%, 60%, 0.1)`)
  gradient.addColorStop(1, `hsla(${(progress * 360 + 60) % 360}, 70%, 40%, 0.05)`)
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
  
  // 绘制动态几何图形
  const numShapes = 8
  for (let i = 0; i < numShapes; i++) {
    const angle = (i / numShapes) * Math.PI * 2 + progress * Math.PI * 2
    const radius = 100 + progress * 150
    const x = centerX + Math.cos(angle) * radius
    const y = centerY + Math.sin(angle) * radius
    
    ctx.beginPath()
    ctx.arc(x, y, 20 + progress * 30, 0, Math.PI * 2)
    ctx.fillStyle = `hsla(${(progress * 360 + i * 45) % 360}, 70%, 60%, ${0.3 + progress * 0.4})`
    ctx.fill()
    
    // 连接线
    if (i > 0) {
      const prevAngle = ((i - 1) / numShapes) * Math.PI * 2 + progress * Math.PI * 2
      const prevX = centerX + Math.cos(prevAngle) * radius
      const prevY = centerY + Math.sin(prevAngle) * radius
      
      ctx.beginPath()
      ctx.moveTo(prevX, prevY)
      ctx.lineTo(x, y)
      ctx.strokeStyle = `hsla(${progress * 360}, 70%, 60%, ${0.2 + progress * 0.3})`
      ctx.lineWidth = 2
      ctx.stroke()
    }
  }
  
  // 中心脉动圆
  ctx.beginPath()
  ctx.arc(centerX, centerY, 30 + Math.sin(progress * Math.PI * 4) * 20, 0, Math.PI * 2)
  ctx.fillStyle = `hsla(${progress * 360}, 80%, 70%, ${0.6 + progress * 0.4})`
  ctx.fill()
}

// 滚动处理
const handleScroll = () => {
  if (!animationArea.value) return
  
  const rect = animationArea.value.getBoundingClientRect()
  const windowHeight = window.innerHeight
  const elementHeight = rect.height
  
  // 计算滚动进度
  const startPoint = windowHeight
  const endPoint = -elementHeight
  const totalDistance = startPoint - endPoint
  const currentPosition = startPoint - rect.top
  const progress = Math.max(0, Math.min(1, currentPosition / totalDistance))
  
  scrollProgress.value = progress
  
  // 更新 canvas
  if (canvasElement.value) {
    const ctx = canvasElement.value.getContext('2d')
    drawFrame(ctx, progress)
  }
  
  // 更新粒子
  updateParticles(progress)
}

// 更新粒子效果
const updateParticles = (progress) => {
  particles.value.forEach((particle, index) => {
    const delay = (index / particles.value.length) * 0.5
    const adjustedProgress = Math.max(0, Math.min(1, (progress - delay) / (1 - delay)))
    
    particle.style = {
      ...particle.style,
      opacity: adjustedProgress * 0.6,
      transform: `translateY(${-adjustedProgress * 100}px) scale(${0.5 + adjustedProgress * 0.5})`
    }
  })
}

// 节流函数
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

const throttledHandleScroll = throttle(handleScroll, 16)

// 响应式画布大小
const updateCanvasSize = () => {
  if (!canvasElement.value) return
  
  const container = canvasElement.value.parentElement
  const size = Math.min(container.clientWidth, container.clientHeight, 800)
  canvasSize.value = { width: size, height: size }
  
  nextTick(() => {
    handleScroll()
  })
}

// 生命周期
onMounted(() => {
  createParticles()
  updateCanvasSize()
  
  window.addEventListener('scroll', throttledHandleScroll, { passive: true })
  window.addEventListener('resize', updateCanvasSize, { passive: true })
  
  // 初始绘制
  handleScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledHandleScroll)
  window.removeEventListener('resize', updateCanvasSize)
})
</script>

<style scoped>
.enhanced-scroll-container {
  position: relative;
  width: 100%;
}

.particles-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: float linear infinite;
}

@keyframes float {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) scale(1);
    opacity: 0;
  }
}

.main-animation-area {
  height: 500vh;
  position: relative;
  z-index: 2;
  width: 100vw;
}

.sticky-content {
  position: sticky;
  top: 0;
  height: 100vh;
  width: 100vw;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr;
  gap: 2rem;
  padding: 2rem;
  align-items: center;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

.animation-canvas {
  position: relative;
  justify-self: end;
  width: 100%;
  max-width: 600px;
}

.frame-canvas {
  display: block;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  width: 100%;
  height: auto;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  pointer-events: none;
}

.dynamic-text {
  justify-self: start;
  color: white;
  transition: all 0.3s ease;
  max-width: 500px;
}

.dynamic-text h2 {
  font-size: 3.5rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.dynamic-text p {
  font-size: 1.3rem;
  margin: 0;
  opacity: 0.8;
  line-height: 1.4;
}

.enhanced-progress {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.progress-circle {
  width: 80px;
  height: 80px;
  transition: transform 0.3s ease;
}

.progress-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.scroll-indicator {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  z-index: 100;
  animation: bounce 2s infinite;
}

.scroll-mouse {
  width: 30px;
  height: 50px;
  border: 2px solid rgba(255,255,255,0.6);
  border-radius: 15px;
  margin: 0 auto 1rem;
  position: relative;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background: rgba(255,255,255,0.6);
  border-radius: 2px;
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  animation: scroll-wheel 2s infinite;
}

@keyframes scroll-wheel {
  0% {
    top: 8px;
    opacity: 1;
  }
  100% {
    top: 24px;
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sticky-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .animation-canvas {
    justify-self: center;
    max-width: 400px;
  }

  .dynamic-text {
    justify-self: center;
    text-align: center;
    max-width: 100%;
  }

  .dynamic-text h2 {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .sticky-content {
    padding: 1rem;
    gap: 1rem;
  }

  .animation-canvas {
    max-width: 300px;
  }

  .dynamic-text h2 {
    font-size: 2rem;
  }

  .dynamic-text p {
    font-size: 1.1rem;
  }

  .enhanced-progress {
    bottom: 1rem;
    right: 1rem;
  }

  .progress-circle {
    width: 60px;
    height: 60px;
  }

  .progress-percentage {
    font-size: 0.8rem;
  }
}
</style>
