<script setup>
import ScrollFrameAnimation from './components/ScrollFrameAnimation.vue'
import EnhancedScrollAnimation from './components/EnhancedScrollAnimation.vue'
import { ref } from 'vue'

const currentDemo = ref('enhanced') // 'basic' 或 'enhanced'

const switchDemo = (demo) => {
  currentDemo.value = demo
}
</script>

<template>
  <div class="app">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <h1>滚动驱动动画演示</h1>
        <p>向下滚动查看逐帧动画效果</p>

        <!-- 切换按钮 -->
        <div class="demo-switcher">
          <button
            @click="switchDemo('basic')"
            :class="{ active: currentDemo === 'basic' }"
            class="demo-btn"
          >
            基础版本
          </button>
          <button
            @click="switchDemo('enhanced')"
            :class="{ active: currentDemo === 'enhanced' }"
            class="demo-btn"
          >
            增强版本
          </button>
        </div>
      </div>
    </nav>

    <!-- 滚动驱动的逐帧动画组件 -->
    <ScrollFrameAnimation v-if="currentDemo === 'basic'" />
    <EnhancedScrollAnimation v-if="currentDemo === 'enhanced'" />

    <!-- 底部内容 -->
    <section class="bottom-content">
      <div class="content-wrapper">
        <h2>动画已完成</h2>
        <p>这是一个使用滚动驱动的逐帧动画演示，类似于 Adaline.ai 网站的效果。</p>
        <p>动画通过监听页面滚动位置来控制帧的切换，创造出流畅的视觉体验。</p>

        <div class="features">
          <h3>特性介绍</h3>
          <ul>
            <li>🎨 <strong>基础版本</strong>：使用预生成的 SVG 图片帧实现滚动动画</li>
            <li>✨ <strong>增强版本</strong>：使用 Canvas 实时绘制，包含粒子效果和动态文本</li>
            <li>📱 响应式设计，支持移动端</li>
            <li>⚡ 高性能滚动监听，60fps 流畅体验</li>
            <li>🎯 精确的滚动进度计算</li>
          </ul>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
}

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
  color: white;
}

.nav-content h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.nav-content p {
  margin: 0.5rem 0 1rem 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.demo-switcher {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin-top: 1rem;
}

.demo-btn {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.demo-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.demo-btn.active {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.bottom-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
  position: relative;
  z-index: 10;
}

.content-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content-wrapper h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.content-wrapper p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.features {
  margin-top: 3rem;
  text-align: left;
}

.features h3 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.features ul {
  list-style: none;
  padding: 0;
  max-width: 600px;
  margin: 0 auto;
}

.features li {
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 1rem;
  line-height: 1.5;
}

.features li:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }

  .nav-content h1 {
    font-size: 1.2rem;
  }

  .demo-switcher {
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
  }

  .demo-btn {
    width: 120px;
  }

  .content-wrapper {
    padding: 0 1rem;
  }

  .content-wrapper h2 {
    font-size: 2rem;
  }

  .features {
    text-align: center;
  }

  .features ul {
    text-align: left;
  }
}
</style>
