<script setup>
import ScrollFrameAnimation from './components/ScrollFrameAnimation.vue'
import EnhancedScrollAnimation from './components/EnhancedScrollAnimation.vue'
import SimpleScrollTest from './components/SimpleScrollTest.vue'
import { ref } from 'vue'

const currentDemo = ref('simple') // 'basic', 'enhanced', 'simple'

const switchDemo = (demo) => {
  currentDemo.value = demo
}
</script>

<template>
  <div class="app">
    <!-- 全屏滚动驱动的逐帧动画组件 -->
    <SimpleScrollTest v-if="currentDemo === 'simple'" />
    <ScrollFrameAnimation v-if="currentDemo === 'basic'" />
    <EnhancedScrollAnimation v-if="currentDemo === 'enhanced'" />

    <!-- 固定的控制面板 -->
    <div class="control-panel">
      <div class="demo-switcher">
        <button
          @click="switchDemo('simple')"
          :class="{ active: currentDemo === 'simple' }"
          class="demo-btn"
        >
          简单测试
        </button>
        <button
          @click="switchDemo('basic')"
          :class="{ active: currentDemo === 'basic' }"
          class="demo-btn"
        >
          基础版本
        </button>
        <button
          @click="switchDemo('enhanced')"
          :class="{ active: currentDemo === 'enhanced' }"
          class="demo-btn"
        >
          增强版本
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

.control-panel {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.demo-switcher {
  display: flex;
  gap: 0.5rem;
}

.demo-btn {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  white-space: nowrap;
}

.demo-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.demo-btn.active {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    padding: 0.8rem;
  }

  .demo-switcher {
    justify-content: center;
  }

  .demo-btn {
    flex: 1;
    text-align: center;
  }
}
</style>
