<template>
  <div class="simple-scroll-container">
    <!-- 滚动区域 -->
    <div class="scroll-area" ref="scrollArea">
      <div class="sticky-wrapper">
        <!-- 左侧内容 -->
        <div class="content-side">
          <div class="content-item" v-if="scrollProgress > 0.1" :style="getContentStyle(0.1)">
            <h1>滚动驱动动画测试</h1>
            <p>使用您的 4K 图片</p>
          </div>
          
          <div class="content-item" v-if="scrollProgress > 0.3" :style="getContentStyle(0.3)">
            <h2>特性展示</h2>
            <ul>
              <li>✨ 全屏布局</li>
              <li>🎨 图片滤镜效果</li>
              <li>📱 响应式设计</li>
            </ul>
          </div>
          
          <div class="content-item" v-if="scrollProgress > 0.6" :style="getContentStyle(0.6)">
            <h2>技术实现</h2>
            <p>基于滚动进度的动态效果</p>
          </div>
          
          <div class="content-item" v-if="scrollProgress > 0.9" :style="getContentStyle(0.9)">
            <h2>🎉 完成</h2>
            <p>滚动动画演示完成</p>
          </div>
        </div>
        
        <!-- 右侧图片 -->
        <div class="image-side">
          <img 
            src="/src/assets/img/4k.jpg"
            alt="4K Image"
            class="main-image"
            :style="imageStyle"
          />
        </div>
      </div>
    </div>
    
    <!-- 调试信息 -->
    <div class="debug-panel">
      <p>滚动进度: {{ Math.round(scrollProgress * 100) }}%</p>
      <p>页面可滚动: {{ isScrollable ? '是' : '否' }}</p>
    </div>
    
    <!-- 滚动提示 -->
    <div class="scroll-hint" v-if="scrollProgress < 0.05">
      <div class="scroll-arrow">↓</div>
      <p>向下滚动</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 响应式数据
const scrollArea = ref(null)
const scrollProgress = ref(0)
const isScrollable = ref(false)

// 计算属性
const imageStyle = computed(() => {
  const progress = scrollProgress.value
  return {
    transform: `scale(${1 + progress * 0.2}) rotate(${progress * 5}deg)`,
    filter: `
      brightness(${0.8 + progress * 0.4}) 
      contrast(${0.9 + progress * 0.3}) 
      saturate(${0.7 + progress * 0.6})
      hue-rotate(${progress * 45}deg)
    `,
    opacity: 0.8 + progress * 0.2
  }
})

// 内容样式
const getContentStyle = (threshold) => {
  const progress = Math.max(0, Math.min(1, (scrollProgress.value - threshold) / 0.2))
  return {
    opacity: progress,
    transform: `translateY(${(1 - progress) * 30}px)`,
    transition: 'all 0.6s ease-out'
  }
}

// 滚动处理
const handleScroll = () => {
  if (!scrollArea.value) return
  
  const rect = scrollArea.value.getBoundingClientRect()
  const windowHeight = window.innerHeight
  const elementHeight = rect.height
  
  // 检查是否可滚动
  isScrollable.value = elementHeight > windowHeight
  
  // 计算滚动进度
  const startPoint = windowHeight
  const endPoint = -elementHeight
  const totalDistance = startPoint - endPoint
  const currentPosition = startPoint - rect.top
  const progress = Math.max(0, Math.min(1, currentPosition / totalDistance))
  
  scrollProgress.value = progress
  
  console.log('Scroll Progress:', progress, 'Position:', currentPosition, 'Total:', totalDistance)
}

// 节流函数
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

const throttledHandleScroll = throttle(handleScroll, 16)

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', throttledHandleScroll, { passive: true })
  window.addEventListener('resize', throttledHandleScroll, { passive: true })
  
  // 初始检查
  setTimeout(() => {
    handleScroll()
  }, 100)
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledHandleScroll)
  window.removeEventListener('resize', throttledHandleScroll)
})
</script>

<style scoped>
.simple-scroll-container {
  width: 100%;
  min-height: 100vh;
}

.scroll-area {
  height: 400vh; /* 4倍视口高度 */
  position: relative;
}

.sticky-wrapper {
  position: sticky;
  top: 0;
  height: 100vh;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  align-items: center;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

.content-side {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  color: white;
}

.content-item {
  opacity: 0;
  transform: translateY(30px);
}

.content-item h1 {
  font-size: 3rem;
  margin: 0 0 1rem 0;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.content-item h2 {
  font-size: 2rem;
  margin: 0 0 1rem 0;
  color: #667eea;
}

.content-item p {
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.9;
  margin: 0;
}

.content-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.content-item li {
  padding: 0.5rem 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.image-side {
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-image {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  transition: all 0.1s ease-out;
}

.debug-panel {
  position: fixed;
  top: 1rem;
  left: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-family: monospace;
  font-size: 0.9rem;
  z-index: 1000;
}

.debug-panel p {
  margin: 0.5rem 0;
}

.scroll-hint {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  z-index: 100;
  animation: bounce 2s infinite;
}

.scroll-arrow {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.scroll-hint p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sticky-wrapper {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: 1rem;
  }
  
  .content-item h1 {
    font-size: 2.5rem;
  }
  
  .content-item h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .sticky-wrapper {
    padding: 1rem;
  }
  
  .content-item h1 {
    font-size: 2rem;
  }
  
  .content-item h2 {
    font-size: 1.5rem;
  }
  
  .main-image {
    max-width: 300px;
  }
}
</style>
